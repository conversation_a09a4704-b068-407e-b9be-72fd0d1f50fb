new FroalaEditor('#content', {
    fileUpload: false,
    videoUpload: true,
    videoUploadURL: window.location.href,
    videoUploadParams: {
        froala: true
    },
    imageUploadURL: window.location.href,
    imageUploadParams: {
        froala: true
    },
    toolbarButtons: {
        moreText: {
            buttons: ['bold', 'italic', 'underline', 'strikeThrough', 'subscript', 'superscript', 'fontFamily', 'fontSize', 'textColor', 'backgroundColor', 'inlineClass', 'inlineStyle', 'clearFormatting']
        },
        moreParagraph: {
            buttons: ['alignLeft', 'alignCenter', 'formatOLSimple', 'alignRight', 'alignJustify', 'formatOL', 'formatUL', 'paragraphFormat', 'paragraphStyle', 'lineHeight', 'outdent', 'indent', 'quote']
        },
        moreRich: {
            buttons: ['insertLink', 'insertImage', 'insertVideo', 'insertTable', 'emoticons', 'fontAwesome', 'specialCharacters', 'embedly', 'insertFile', 'insertHR']
        },
        moreMisc: {
            buttons: ['undo', 'redo', 'fullscreen', 'print', 'getPDF', 'spellChecker', 'selectAll', 'html', 'help']
        }
    },
    events: {
        'video.uploaded': function(response) {
            console.log('Video uploaded successfully', response);
            if (response.link) {
                console.log('Video URL:', response.link);
            }
        },
        'video.upload.error': function(response) {
            console.log('Error during video upload', response);
        },
        'image.uploaded': function(response) {
            console.log('Image uploaded successfully', response);
            if (response.link) {
                console.log('Image URL:', response.link);
            }
        },
        'image.upload.error': function(response) {
            console.log('Error during image upload', response);
        }
    }
});

// Image preview
document.getElementById('image').addEventListener('change', function (event) {
    const file = event.target.files[0];
    const preview = document.getElementById('preview');
    const imagePreview = document.getElementById('image-preview');

    if (file) {
        const reader = new FileReader();
        reader.onload = function (e) {
            preview.src = e.target.result;
            imagePreview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    } else {
        preview.src = '#';
        imagePreview.style.display = 'none';
    }
});

// Remove Froala warning
document.addEventListener('DOMContentLoaded', function() {
    const froalaWarning = document.querySelector('div[style*="z-index:9999"]');
    if (froalaWarning) {
        froalaWarning.remove();
    }
});