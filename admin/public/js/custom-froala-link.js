(function (e, t) {
  if (typeof exports === "object" && typeof module !== "undefined") {
    t(require("froala-editor"));
  } else if (typeof define === "function" && define.amd) {
    define(["froala-editor"], t);
  } else {
    t(e.FroalaEditor);
  }
})(this, function (FroalaEditor) {
  "use strict";
  FroalaEditor = FroalaEditor && FroalaEditor.hasOwnProperty("default") ? FroalaEditor["default"] : FroalaEditor;

  // Popup template customization
  Object.assign(FroalaEditor.POPUP_TEMPLATES, {
    "link.edit": "[_BUTTONS_]",
    "link.insert": "[_BUTTONS_][_INPUT_LAYER_]"
  });

  // Button configuration
  Object.assign(FroalaEditor.DEFAULTS, {
    linkEditButtons: ["linkOpen", "linkStyle", "linkEdit", "linkRemove"],
    linkInsertButtons: ["linkBack", "|", "linkList", "linkInsert"]
  });

  // Register custom insert link button (optional)
  FroalaEditor.RegisterCommand('customLink', {
    title: 'Insert Link',
    icon: 'insertLink',
    undo: true,
    focus: true,
    plugin: 'link',
    callback: function () {
      this.link.showInsertPopup();
    }
  });
});
