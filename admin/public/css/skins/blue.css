.bg-primary {
  background-color: #3F52E3 !important;
}
.text-primary {
  color: #3F52E3 !important;
}
a {
  color: #3F52E3;
}
a:hover {
  color: #2d3db8;
}
.btn-primary {
  background-color: #3F52E3;
  border-color: transparent !important;
}
.btn-primary:focus,
.btn-primary:focus:active,
.btn-primary:active,
.btn-primary:hover {
  background-color: #2d3db8 !important;
}
.btn-primary.disabled, .btn-primary:disabled {
  background-color: #3F52E3;
  border-color: #3F52E3;
}
.btn-outline-primary {
  color: #3F52E3;
  background-color: transparent;
  background-image: none;
  border-color: #3F52E3;
}

.btn-outline-primary:hover {
  color: #fff;
  background-color: #3F52E3;
  border-color: #3F52E3;
}
.btn-outline-primary.disabled, .btn-outline-primary:disabled {
  color: #3F52E3;
  background-color: transparent;
}
.btn-outline-primary:not([disabled]):not(.disabled):active, .btn-outline-primary:not([disabled]):not(.disabled).active,
.show > .btn-outline-primary.dropdown-toggle {
  color: #fff;
  background-color: #3F52E3;
  border-color: #3F52E3;
}
.btn-link {
  font-weight: 400;
  color: #3F52E3;
  background-color: transparent;
}
.btn-link:hover {
  color: #2d3db8;
}
.dropdown-item.active, .dropdown-item:active {
  color: #fff;
  background-color: #3F52E3;
}
.custom-control-input:checked ~ .custom-control-label::before {
  color: #fff;
  background-color: #3F52E3;
}
.custom-checkbox .custom-control-input:checked ~ .custom-control-label::before {
  background-color: #3F52E3;
}
.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::before {
  background-color: #3F52E3;
}
.custom-radio .custom-control-input:checked ~ .custom-control-label::before {
  background-color: #3F52E3;
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #fff;
  background-color: #3F52E3;
}
.page-link {
  color: #3F52E3;
  background-color: #fff;
  border: 1px solid #ededed;
}
.page-item.active .page-link {
  color: #fff;
  background-color: #3F52E3;
  border-color: #3F52E3;
}
.page-link:focus, .page-link:hover {
  color: #2d3db8;
}

.badge-primary {
  color: #fff;
  background-color: #3F52E3;
}
.progress-bar {
  color: #fff;
  background-color: #3F52E3;
}
.list-group-item.active {
  color: #fff;
  background-color: #3F52E3;
  border-color: #3F52E3;
}
.bg-primary {
  background-color: #3F52E3 !important;
}
.border-primary {
  border-color: #3F52E3 !important;
}
.text-primary {
  color: #3F52E3 !important;
}

.navbar.active {
  background-color: #3F52E3;
}
.navbar-bg {
  background-color: #3F52E3;
}
.form-control:focus {
  border-color: #3F52E3;
}
.main-sidebar .sidebar-menu li.active a {
  background-color: #f9f9f9;
  color: #3F52E3;
}
.main-sidebar .sidebar-menu li ul.menu-dropdown li a:hover {
  color: #3F52E3;
}

.main-sidebar .sidebar-menu li ul.menu-dropdown li.active a {
  color: #3F52E3;
}
.alert.alert-primary {
  background-color: #3F52E3;
}
.card.card-primary {
  border-top: 2px solid #3F52E3;
}
.fc button.fc-state-active {
  background-color: #3F52E3;
  color: #fff;
}
.jqvmap-circle {
  background-color: #3F52E3;
  border: 1px solid #000;
}
.weather ul li {
  border: 2px solid #3F52E3;
  color: #3F52E3;
}
.card-chat .chat-content .chat-item.chat-right .chat-details .chat-text {
  background-color: #3F52E3;
  color: #fff;
}
.nav-tabs .nav-item .nav-link {
  color: #3F52E3;
}
.nav-pills .nav-item .nav-link {
  color: #3F52E3;
}
.swal-button.swal-button--confirm {
  background-color: #3F52E3;
}
.page-item .page-link {
  color: #3F52E3;
}
.page-item.active .page-link {
  background-color: #3F52E3;
  border-color: #3F52E3;
}
.btn-group .btn.active {
  background-color: #3F52E3;
  color: #fff;
}
.media .media-right {
  color: #3F52E3;
}
.selectric-items li.selected,
.selectric-items li.highlighted {
  background-color: #3F52E3;
  color: #fff;
}
.dropzone {
  border: 2px dashed #3F52E3;
}
.accordion .accordion-header[aria-expanded="true"] {
  background-color: #3F52E3;
  color: #fff;
}
.bootstrap-tagsinput .tag {
  background-color: #3F52E3;
}
