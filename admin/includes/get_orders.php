<?php
header('Content-Type: application/json');
include 'connect.php';

$period = $_GET['period'] ?? 'week';

switch ($period) {
  case 'month':
    $sql = "
      SELECT 
          DAYNAME(created_at) AS day, 
          COUNT(*) AS total_orders 
      FROM 
          orders 
      WHERE 
          created_at >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)
      GROUP BY 
          DAYNAME(created_at)
      ORDER BY 
          FIELD(day, 'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday')
    ";
    break;
  case 'year':
    $sql = "
      SELECT 
          MONTHNAME(created_at) AS month, 
          COUNT(*) AS total_orders 
      FROM 
          orders 
      WHERE 
          created_at >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)
      GROUP BY 
          MONTHNAME(created_at)
      ORDER BY 
          MONTH(created_at)
    ";
    break;
  default:
    $sql = "
      SELECT 
          DAYNAME(created_at) AS day, 
          COUNT(*) AS total_orders 
      FROM 
          orders 
      WHERE 
          created_at >= DATE_SUB(CURDATE(), INTERVAL 1 WEEK)
      GROUP BY 
          DAYNAME(created_at)
      ORDER BY 
          FIELD(day, 'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday')
    ";
    break;
}

$stmt = $pdo->prepare($sql);
$stmt->execute();
$orders = $stmt->fetchAll(PDO::FETCH_ASSOC);

$order_count = array_fill_keys(['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'], 0);
foreach ($orders as $order) {
    $order_count[$order['day']] = (int)$order['total_orders'];
}

echo json_encode(array_values($order_count));
?>